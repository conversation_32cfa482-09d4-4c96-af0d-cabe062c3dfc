import { authTables } from "@convex-dev/auth/server";
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
export const userRoleEnum = v.union(
  v.literal("author"),
  v.literal("admin"),
  v.literal("media-manager"),
  v.literal("ads-manager")
);
//  make status enum
export const statusEnum = v.union(
  v.literal("draft"),
  v.literal("staged"),
  v.literal("approved"),
  v.literal("rejected"),
  v.literal("published"),
  v.literal("deleted")
);
export const actionEnum = v.union(
  v.literal("created"),
  v.literal("updated"),
  v.literal("added-to-favorite"),
  v.literal("removed-from-favorite"),
  v.literal("deleted"),
  v.literal("restored"),
  v.literal("staged"),
  v.literal("unstaged"),
  v.literal("unapproved"),
  v.literal("approved"),
  v.literal("rejected"),
  v.literal("published"),
  v.literal("unpublished"),
  v.literal("group-added"),
  v.literal("group-removed"),
  v.literal("cover-added"),
  v.literal("cover-removed")
);

export const ActivityTarget = v.union(
  v.object({
    docType: v.literal("user"),
    docId: v.id("users"),
  }),
  v.object({
    docType: v.literal("article"),
    docId: v.id("articles"),
  }),
  v.object({
    docType: v.literal("group"),
    docId: v.id("groups"),
  }),
  v.object({
    docType: v.literal("mediaFile"),
    docId: v.id("mediaFiles"),
  }),
  v.object({
    docType: v.literal("board"),
    docId: v.id("boards"),
  }),
  v.object({
    docType: v.literal("boardColumn"),
    docId: v.id("boardColumns"),
  }),
  v.object({
    docType: v.literal("columnTask"),
    docId: v.id("columnTasks"),
  })
);
const applicationTables = {
  // Groups table — for categories like "Sports", "Culture"
  groups: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    status: statusEnum,
  })
    .index("by_name", ["name"])
    .searchIndex("search_name", {
      searchField: "name",
    }),

  // article table
  articles: defineTable({
    title: v.string(),
    slug: v.string(),
    description: v.optional(v.string()),
    content: v.optional(v.string()),
    words: v.optional(v.number()),
    coverImage: v.optional(v.string()),
    status: statusEnum,
    isFavorite: v.optional(v.boolean()),
    rejectionMessage: v.optional(v.string()),
    userId: v.id("users"),
    groupId: v.optional(v.id("groups")),
    updatedAt: v.optional(v.number()),
  })
    .index("by_userId", ["userId"])
    .index("by_groupId", ["groupId"])
    .index("by_status", ["status"])
    .index("by_slug", ["slug"])
    .index("by_status_userId", ["status", "userId"])
    .searchIndex("search_title", {
      searchField: "title",
    }),
  // media table

  mediaFiles: defineTable({
    userId: v.id("users"),
    title: v.string(),
    slug: v.string(),
    status: statusEnum,
    key: v.string(),
    bucket: v.string(),
    description: v.optional(v.string()),
    isFavorite: v.optional(v.boolean()),
    groupId: v.optional(v.id("groups")),
    updatedAt: v.optional(v.number()),
  })
    .index("bucket_key", ["bucket", "key"])
    .index("by_userId", ["userId"])
    .index("by_groupId", ["groupId"])
    .index("by_slug", ["slug"])
    .index("by_status", ["status"])
    .index("by_status_userId", ["status", "userId"])
    .searchIndex("search_title", {
      searchField: "title",
    }),

  // activity table
  activities: defineTable({
    userId: v.id("users"),
    target: ActivityTarget,
    docTitle: v.string(),
    docStatus: v.string(),
    action: actionEnum,
  })
    .index("by_userId", ["userId"])
    .index("by_target", ["target.docType", "target.docId"]),

  // chat table

  chatMessages: defineTable({
    senderId: v.id("users"),
    senderName: v.string(),
    receiverId: v.id("users"),
    receiverName: v.string(),
    content: v.string(),
    isRead: v.boolean(),
    // add is deleted default to false
    isDeleted: v.boolean(),
    updatedAt: v.optional(v.number()),
  })
    .index("by_sender", ["senderId"])
    .index("by_receiver", ["receiverId"])
    .index("by_conversation", ["senderId", "receiverId"])
    .searchIndex("search_senderName", {
      searchField: "senderName",
    }),

  // flowboard tables
  // 1. Boards
  // boards: defineTable({
  //   adminId: v.id("users"), // owner/admin
  //   name: v.string(),
  //   createdAt: v.number(),
  //   updatedAt: v.optional(v.number()),
  // }),

  // 2. Columns
  // boardColumns: defineTable({
  //   boardId: v.id("boards"),
  //   title: v.string(), // e.g. Backlog, In Progress, Done
  //   // order: v.number(), // position in board
  //   createdAt: v.number(),
  //   updatedAt: v.optional(v.number()),
  // }).index("by_boardId", ["boardId"]),

  // 3. Tasks
  // columnTasks: defineTable({
  //   boardId: v.id("boards"), // for quick filtering
  //   columnId: v.id("boardColumns"), // where it lives
  //   title: v.string(),
  //   description: v.optional(v.string()),
  //   priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high")),
  //   assignedTo: v.optional(v.id("users")),
  //   dueDate: v.optional(v.string()), // store ISO date string (YYYY-MM-DD)
  //   createdAt: v.number(),
  //   updatedAt: v.optional(v.number()),
  // })
  //   .index("by_boardId", ["boardId"])
  //   .index("by_columnId", ["columnId"]),
};

export default defineSchema({
  ...authTables,
  users: defineTable({
    name: v.optional(v.string()),
    image: v.optional(v.string()),
    email: v.optional(v.string()),
    emailVerificationTime: v.optional(v.number()),
    phone: v.optional(v.string()),
    phoneVerificationTime: v.optional(v.number()),
    isAnonymous: v.optional(v.boolean()),
    // Custom field.
    username: v.optional(v.string()),
    bio: v.optional(v.string()),
    role: v.optional(userRoleEnum),
    imageId: v.optional(v.id("_storage")),
    otherEmails: v.optional(
      v.array(
        v.object({
          email: v.string(),
          isVerified: v.boolean(),
          isPrimary: v.boolean(),
        })
      )
    ),
    verified: v.optional(v.boolean()),
  })
    .index("email", ["email"])
    .index("phone", ["phone"])
    .index("by_role", ["role"])
    .searchIndex("search_username", {
      searchField: "username",
    }),
  ...applicationTables,
});
