import type { Row } from '@tanstack/react-table';
import { type ReactMutation, useMutation, useQuery } from 'convex/react';
import type { FunctionReference } from 'convex/server';
import {
  CircleCheckBigIcon,
  MoreHorizontal,
  PenToolIcon,
  StarIcon,
  TrashIcon,
  UndoIcon,
} from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import AddGroupToMedia from './add-group';
import UpdateMedia from './create-edit-media';
import type { TMedia } from './types';

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
}
export default function MediaTableColumnActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const media = row.original as TMedia;
  const user = useQuery(api.users.getUser);

  // Media Mutations
  const deleteMedia = useMutation(api.media.deleteMediaFileTemp);
  const restoreMedia = useMutation(api.media.restoreMediaFile);
  const toggleFavoriteMedia = useMutation(api.media.toggleFavoriteMediaFile);
  const stageMedia = useMutation(api.media.stageMediaFile);
  const unStageMedia = useMutation(api.media.unStageMediaFile);

  // Admin mutations
  const approveMedia = useMutation(api.admin.approveMediaFile);
  const rejectMedia = useMutation(api.admin.rejectMediaFile);
  const publishMedia = useMutation(api.admin.publishMediaFile);
  const unapproveMedia = useMutation(api.admin.unapproveMediaFile);

  const [openEditDialog, setOpenEditDialog] = useState(false);

  const handleAction = useCallback(
    async (
      mutation: ReactMutation<
        FunctionReference<
          'mutation',
          'public',
          {
            id: Id<'mediaFiles'>;
          },
          | {
              success: boolean;
              error: string;
            }
          | {
              success: boolean;
              error?: undefined;
            },
          string | undefined
        >
      >,
      params: { id: Id<'mediaFiles'> },
      successMsg: string,
      errorMsg: string
    ) => {
      try {
        if (mutation === publishMedia) {
          if (!('success' in media || media.groupId)) {
            toast.error('Media must be in a group to be published.');
            return;
          }
          if (!('success' in media || media.url)) {
            toast.error('Media must have a url to be published.');
            return;
          }
        }
        await mutation(params);
        toast.success(successMsg);
      } catch {
        toast.error(errorMsg);
      }
    },
    [media, publishMedia]
  );
  if (!media || 'success' in media) {
    return null;
  }
  if (!user || 'success' in user) {
    return null;
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            className="flex h-8 w-8 cursor-pointer p-0 data-[state=open]:bg-muted"
            variant="ghost"
          >
            <MoreHorizontal className="size-4" />
            <span className="sr-only">Open staff actions</span>
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end" className="w-56 px-2 py-4">
          {/* Favorite toggle */}
          <DropdownMenuItem
            className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
            onClick={() =>
              handleAction(
                toggleFavoriteMedia,
                { id: media._id },
                media.isFavorite
                  ? ' Media removed from favorite!'
                  : 'Media added to favorite!',
                'Failed to toggle favorite media.'
              )
            }
          >
            <span>
              {media.isFavorite ? 'Remove from favorite' : 'Add to favorite'}
            </span>
            <StarIcon
              className={`size-4 ${
                media.isFavorite
                  ? 'fill-primary text-muted-foreground'
                  : 'text-muted-foreground'
              }`}
            />
          </DropdownMenuItem>

          {/* Edit */}
          <DropdownMenuItem
            className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
            onClick={() => setOpenEditDialog(true)}
          >
            <span>Edit</span>
            <PenToolIcon className="size-4 text-muted-foreground" />
          </DropdownMenuItem>

          {/* Delete / Restore */}
          {media.status === 'deleted' ? (
            <DropdownMenuItem
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={() =>
                handleAction(
                  restoreMedia,
                  { id: media._id },
                  'Media restored successfully!',
                  'Failed to restore media.'
                )
              }
            >
              <span>Restore</span>
              <UndoIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              disabled={
                media.status === 'published' || media.status === 'approved'
              }
              onClick={() =>
                handleAction(
                  deleteMedia,
                  { id: media._id },
                  'Media deleted successfully!',
                  'Failed to delete media.'
                )
              }
            >
              <span>Delete</span>
              <TrashIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          )}

          {/* Author actions */}
          {user.role === 'media-manager' && (
            <div>
              {media.status === 'staged' ? (
                <DropdownMenuItem
                  className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                  onClick={() =>
                    handleAction(
                      unStageMedia,
                      { id: media._id },
                      'Media unstaged successfully!',
                      'Failed to unstage media.'
                    )
                  }
                >
                  <span>Unstage</span>
                  <UndoIcon className="size-4 text-muted-foreground" />
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem
                  className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                  disabled={
                    media.status === 'published' || media.status === 'approved'
                  }
                  onClick={() =>
                    handleAction(
                      stageMedia,
                      { id: media._id },
                      'Media staged successfully!',
                      'Failed to stage media.'
                    )
                  }
                >
                  <span>Stage</span>
                  <CircleCheckBigIcon className="size-4 text-muted-foreground" />
                </DropdownMenuItem>
              )}
            </div>
          )}

          {/* Admin actions */}
          {user.role === 'admin' && (
            <>
              {media.status === 'staged' && (
                <>
                  <DropdownMenuItem
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    onClick={() =>
                      handleAction(
                        rejectMedia,
                        { id: media._id },
                        'Media rejected successfully!',
                        'Failed to reject media.'
                      )
                    }
                  >
                    <span>Reject</span>
                    <TrashIcon className="size-4 text-muted-foreground" />
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    onClick={() =>
                      handleAction(
                        approveMedia,
                        { id: media._id },
                        'Media approved successfully!',
                        'Failed to approve media.'
                      )
                    }
                  >
                    <span>Approve</span>
                    <CircleCheckBigIcon className="size-4 text-muted-foreground" />
                  </DropdownMenuItem>
                </>
              )}

              {media.status === 'approved' && (
                <>
                  <DropdownMenuItem
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    onClick={() =>
                      handleAction(
                        unapproveMedia,
                        { id: media._id },
                        'Media unapproved successfully!',
                        'Failed to unapprove media.'
                      )
                    }
                  >
                    <span>Unapprove</span>
                    <UndoIcon className="size-4 text-muted-foreground" />
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    onClick={() =>
                      handleAction(
                        publishMedia,
                        { id: media._id },
                        'Media published successfully!',
                        'Failed to publish media.'
                      )
                    }
                  >
                    <span>Publish</span>
                    <CircleCheckBigIcon className="size-4 text-muted-foreground" />
                  </DropdownMenuItem>
                </>
              )}

              {media.status === 'published' && (
                <DropdownMenuItem
                  className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                  onClick={() =>
                    handleAction(
                      unapproveMedia,
                      { id: media._id },
                      'Media unpublished successfully!',
                      'Failed to unpublish media.'
                    )
                  }
                >
                  <span>Unpublish</span>
                  <UndoIcon className="size-4 text-muted-foreground" />
                </DropdownMenuItem>
              )}
              <AddGroupToMedia
                mediaId={media._id}
                prevGroupId={media.groupId as Id<'groups'>}
                status={media.status}
              />
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
      <UpdateMedia
        id={media._id}
        openDialog={openEditDialog}
        setOpenDialog={setOpenEditDialog}
        title={media.title}
        url={media.url}
      />
    </>
  );
}
