import type { Row } from '@tanstack/react-table';
import { type ReactMutation, useMutation, useQuery } from 'convex/react';
import type { FunctionReference } from 'convex/server';
import {
  CircleCheckBigIcon,
  MoreHorizontal,
  PenToolIcon,
  StarIcon,
  TrashIcon,
  UndoIcon,
} from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import AddCoverToArticle from './add-cover';
import AddGroupToArticle from './add-group';
import type { TArticle } from './article-table';
import CreateOrUpdateArticle from './create-edit-article';

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
}
export default function ArticleTableColumnActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const article = row.original as TArticle;
  const user = useQuery(api.users.getUser);

  const [openEditDialog, setOpenEditDialog] = useState(false);

  // Author Mutations
  const deleteArticle = useMutation(api.articles.deleteArticle);
  const restoreArticle = useMutation(api.articles.restoreArticle);
  const toggleFavoriteArticle = useMutation(api.articles.toggleFavoriteArticle);
  const stageArticle = useMutation(api.articles.stageArticle);
  const unStageArticle = useMutation(api.articles.unStageArticle);

  // Admin mutations
  const approveArticle = useMutation(api.admin.approveArticle);
  const rejectArticle = useMutation(api.admin.rejectArticle);
  const publishArticle = useMutation(api.admin.publishArticle);
  const unapproveArticle = useMutation(api.admin.unapproveArticle);

  // Helper to handle async actions with toast feedback
  const handleAction = useCallback(
    async (
      mutation: ReactMutation<
        FunctionReference<
          'mutation',
          'public',
          {
            id: Id<'articles'>;
          },
          | {
              success: boolean;
              error: string;
            }
          | {
              success: boolean;
              error?: undefined;
            },
          string | undefined
        >
      >,
      params: { id: Id<'articles'> },
      successMsg: string,
      errorMsg: string
    ) => {
      try {
        if (mutation === publishArticle) {
          if (!('success' in article || article.groupId)) {
            toast.error('Article must be in a group to be published.');
            return;
          }
          if (!('success' in article || article.coverImage)) {
            toast.error('Article must have a cover image to be published.');
            return;
          }
        }
        await mutation(params);
        toast.success(successMsg);
      } catch {
        toast.error(errorMsg);
      }
    },
    [article, publishArticle]
  );
  // Guard clauses
  if (!article || 'success' in article) {
    return null;
  }
  if (!user || 'success' in user) {
    return null;
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            className="flex h-8 w-8 cursor-pointer p-0 data-[state=open]:bg-muted"
            variant="ghost"
          >
            <MoreHorizontal className="size-4" />
            <span className="sr-only">Open staff actions</span>
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end" className="w-56 px-2 py-4">
          <DropdownMenuItem
            className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
            onClick={() =>
              handleAction(
                toggleFavoriteArticle,
                { id: article._id },
                article.isFavorite
                  ? 'Article removed from favorite!'
                  : 'Article added to favorite!',
                'Failed to toggle favorite article.'
              )
            }
          >
            <span>
              {article.isFavorite ? 'Remove from favorite' : 'Add to favorite'}
            </span>
            <StarIcon
              className={`size-4 ${
                article.isFavorite
                  ? 'fill-primary text-muted-foreground'
                  : 'text-muted-foreground'
              }`}
            />
          </DropdownMenuItem>

          {/* Edit */}
          <DropdownMenuItem
            className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
            onClick={() => setOpenEditDialog(true)}
          >
            <span>Edit</span>
            <PenToolIcon className="size-4 text-muted-foreground" />
          </DropdownMenuItem>

          {/* Delete / Restore */}
          {article.status === 'deleted' ? (
            <DropdownMenuItem
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              onClick={() =>
                handleAction(
                  restoreArticle,
                  { id: article._id },
                  'Article restored successfully!',
                  'Failed to restore article.'
                )
              }
            >
              <span>Restore</span>
              <UndoIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem
              className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
              disabled={
                article.status === 'published' || article.status === 'approved'
              }
              onClick={() =>
                handleAction(
                  deleteArticle,
                  { id: article._id },
                  'Article deleted successfully!',
                  'Failed to delete article.'
                )
              }
            >
              <span>Delete</span>
              <TrashIcon className="size-4 text-muted-foreground" />
            </DropdownMenuItem>
          )}

          {/* Author actions */}
          {user.role === 'author' && (
            <div>
              {article.status === 'staged' ? (
                <DropdownMenuItem
                  className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                  onClick={() =>
                    handleAction(
                      unStageArticle,
                      { id: article._id },
                      'Article unstaged successfully!',
                      'Failed to unstage article.'
                    )
                  }
                >
                  <span>Unstage</span>
                  <UndoIcon className="size-4 text-muted-foreground" />
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem
                  className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                  disabled={
                    article.status === 'published' ||
                    article.status === 'approved'
                  }
                  onClick={() =>
                    handleAction(
                      stageArticle,
                      { id: article._id },
                      'Article staged successfully!',
                      'Failed to stage article.'
                    )
                  }
                >
                  <span>Stage</span>
                  <CircleCheckBigIcon className="size-4 text-muted-foreground" />
                </DropdownMenuItem>
              )}
            </div>
          )}

          {/* Admin actions */}
          {user.role === 'admin' && (
            <>
              {article.status === 'staged' && (
                <>
                  <DropdownMenuItem
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    onClick={() =>
                      handleAction(
                        rejectArticle,
                        { id: article._id },
                        'Article rejected successfully!',
                        'Failed to reject article.'
                      )
                    }
                  >
                    <span>Reject</span>
                    <TrashIcon className="size-4 text-muted-foreground" />
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    onClick={() =>
                      handleAction(
                        approveArticle,
                        { id: article._id },
                        'Article approved successfully!',
                        'Failed to approve article.'
                      )
                    }
                  >
                    <span>Approve</span>
                    <CircleCheckBigIcon className="size-4 text-muted-foreground" />
                  </DropdownMenuItem>
                </>
              )}

              {article.status === 'approved' && (
                <>
                  <DropdownMenuItem
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    onClick={() =>
                      handleAction(
                        unapproveArticle,
                        { id: article._id },
                        'Article unapproved successfully!',
                        'Failed to unapprove article.'
                      )
                    }
                  >
                    <span>Unapprove</span>
                    <UndoIcon className="size-4 text-muted-foreground" />
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                    onClick={() =>
                      handleAction(
                        publishArticle,
                        { id: article._id },
                        'Article published successfully!',
                        'Failed to publish article.'
                      )
                    }
                  >
                    <span>Publish</span>
                    <CircleCheckBigIcon className="size-4 text-muted-foreground" />
                  </DropdownMenuItem>
                </>
              )}

              {article.status === 'published' && (
                <DropdownMenuItem
                  className="w-full justify-between font-normal text-muted-foreground hover:text-primary"
                  onClick={() =>
                    handleAction(
                      unapproveArticle,
                      { id: article._id },
                      'Article unpublished successfully!',
                      'Failed to unpublish article.'
                    )
                  }
                >
                  <span>Unpublish</span>
                  <UndoIcon className="size-4 text-muted-foreground" />
                </DropdownMenuItem>
              )}
            </>
          )}
          <AddGroupToArticle
            articleId={article._id}
            prevGroupId={article.groupId as Id<'groups'>}
            status={article.status}
          />
          <AddCoverToArticle
            articleId={article._id}
            prevCoverImg={article.coverImage as string}
            status={article.status}
          />
        </DropdownMenuContent>
      </DropdownMenu>
      <CreateOrUpdateArticle
        description={article.description}
        id={article._id}
        openDialog={openEditDialog}
        setOpenDialog={setOpenEditDialog}
        title={article.title}
      />
    </>
  );
}
